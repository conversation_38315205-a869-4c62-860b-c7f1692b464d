<script setup>
import { ref, onMounted } from 'vue'

// 响应式数据
const message = ref('欢迎来到主页面')
const count = ref(0)

// 方法
const increment = () => {
  count.value++
}

// 生命周期
onMounted(() => {
  console.log('MainWeb 组件已挂载')
})
</script>

<template>
  <div class="main-web">
    <div class="container">
      <h1>{{ message }}</h1>

      <div class="content">
        <p>这是主要的业务页面内容</p>

        <div class="counter-section">
          <p>计数器: {{ count }}</p>
          <button @click="increment" class="btn-primary">
            点击增加
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.main-web {
  min-height: 100vh;
  padding: 2rem;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  color: var(--color-heading);
  margin-bottom: 2rem;
  text-align: center;
}

.content {
  background: var(--color-background-soft);
  padding: 2rem;
  border-radius: 8px;
  border: 1px solid var(--color-border);
}

.counter-section {
  margin-top: 2rem;
  text-align: center;
}

.btn-primary {
  background: rgb(55, 109, 210);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  margin-top: 1rem;
  transition: background-color 0.3s;
}

.btn-primary:hover {
  background: rgb(45, 95, 185);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
