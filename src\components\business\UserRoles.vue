<script setup>
import { ref } from 'vue'

const roles = ref([
  { id: 1, name: '超级管理员', description: '拥有所有权限', userCount: 1 },
  { id: 2, name: '管理员', description: '拥有大部分管理权限', userCount: 3 },
  { id: 3, name: '编辑', description: '可以编辑内容', userCount: 5 },
  { id: 4, name: '用户', description: '基础用户权限', userCount: 20 }
])
</script>

<template>
  <div class="user-roles">
    <div class="header">
      <h3>角色管理</h3>
      <el-button type="primary" :icon="Plus">新增角色</el-button>
    </div>
    
    <el-table :data="roles" style="width: 100%">
      <el-table-column prop="name" label="角色名称" />
      <el-table-column prop="description" label="描述" />
      <el-table-column prop="userCount" label="用户数量" />
      <el-table-column label="操作">
        <template #default>
          <el-button size="small" :icon="Edit">编辑</el-button>
          <el-button size="small" type="danger" :icon="Delete">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<style scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
</style>
