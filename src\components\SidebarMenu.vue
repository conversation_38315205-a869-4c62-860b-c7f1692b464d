<script setup>
import { ref } from 'vue'
import { useAuth } from '../composables/useAuth.js'
import { useRouter } from 'vue-router'

const props = defineProps({
  activeMenu: {
    type: String,
    default: 'user-management'
  }
})

const emit = defineEmits(['menu-select'])

const { user, logout } = useAuth()
const router = useRouter()

// 菜单数据
const menuItems = ref([
  {
    key: 'user-management',
    title: '用户管理',
    icon: 'User',
    children: [
      { key: 'user-list', title: '用户列表', icon: 'UserFilled' },
      { key: 'user-roles', title: '角色管理', icon: 'Avatar' },
      { key: 'user-permissions', title: '权限设置', icon: 'Key' }
    ]
  },
  {
    key: 'content-management',
    title: '内容管理',
    icon: 'Document',
    children: [
      { key: 'article-list', title: '文章管理', icon: 'DocumentCopy' },
      { key: 'category-management', title: '分类管理', icon: 'Folder' },
      { key: 'tag-management', title: '标签管理', icon: 'PriceTag' }
    ]
  },
  {
    key: 'system-settings',
    title: '系统设置',
    icon: 'Setting',
    children: [
      { key: 'basic-settings', title: '基础设置', icon: 'Tools' },
      { key: 'email-settings', title: '邮件配置', icon: 'Message' },
      { key: 'backup-restore', title: '备份恢复', icon: 'Download' }
    ]
  },
  {
    key: 'data-analysis',
    title: '数据分析',
    icon: 'DataAnalysis',
    children: [
      { key: 'user-statistics', title: '用户统计', icon: 'TrendCharts' },
      { key: 'content-statistics', title: '内容统计', icon: 'PieChart' },
      { key: 'system-logs', title: '系统日志', icon: 'Document' }
    ]
  }
])

// 处理菜单点击
const handleMenuClick = (menuKey) => {
  emit('menu-select', menuKey)
}

// 登出处理
const handleLogout = async () => {
  await logout()
  router.push('/login')
}
</script>

<template>
  <div class="sidebar-menu">
    <!-- 用户信息区域 -->
    <div class="user-info">
      <div class="user-avatar">
        <el-avatar :size="50" :icon="UserFilled" />
      </div>
      <div class="user-details">
        <div class="user-name">{{ user?.email || '未登录' }}</div>
        <div class="user-role">管理员</div>
      </div>
      <el-button
        type="danger"
        size="small"
        :icon="SwitchButton"
        @click="handleLogout"
        class="logout-btn"
      >
        登出
      </el-button>
    </div>

    <!-- 菜单列表 -->
    <div class="menu-container">
      <el-menu
        :default-active="activeMenu"
        class="sidebar-menu-list"
        @select="handleMenuClick"
        background-color="#ffffff"
        text-color="#606266"
        active-text-color="#409eff"
      >
        <el-sub-menu
          v-for="item in menuItems"
          :key="item.key"
          :index="item.key"
        >
          <template #title>
            <el-icon><component :is="item.icon" /></el-icon>
            <span>{{ item.title }}</span>
          </template>

          <el-menu-item
            v-for="child in item.children"
            :key="child.key"
            :index="child.key"
          >
            <el-icon><component :is="child.icon" /></el-icon>
            <span>{{ child.title }}</span>
          </el-menu-item>
        </el-sub-menu>
      </el-menu>
    </div>
  </div>
</template>

<style scoped>
.sidebar-menu {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.user-info {
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.user-avatar {
  flex-shrink: 0;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-role {
  font-size: 12px;
  opacity: 0.8;
}

.logout-btn {
  flex-shrink: 0;
}

.menu-container {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.sidebar-menu-list {
  border: none;
  height: 100%;
  min-height: calc(100vh - 120px);
}

.sidebar-menu-list .el-sub-menu__title {
  height: 56px;
  line-height: 56px;
  padding-left: 20px !important;
  font-weight: 500;
}

.sidebar-menu-list .el-menu-item {
  height: 48px;
  line-height: 48px;
  padding-left: 50px !important;
}

.sidebar-menu-list .el-menu-item:hover,
.sidebar-menu-list .el-sub-menu__title:hover {
  background-color: #ecf5ff;
}

.sidebar-menu-list .el-menu-item.is-active {
  background-color: #409eff;
  color: white;
}

.sidebar-menu-list .el-menu-item.is-active .el-icon {
  color: white;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .user-info {
    padding: 16px;
  }

  .sidebar-menu-list .el-sub-menu__title {
    height: 52px;
    line-height: 52px;
    padding-left: 18px !important;
  }

  .sidebar-menu-list .el-menu-item {
    height: 46px;
    line-height: 46px;
    padding-left: 45px !important;
  }
}

@media (max-width: 768px) {
  .sidebar-menu {
    height: auto;
    max-height: 200px;
  }

  .user-info {
    padding: 15px;
  }

  .user-name {
    font-size: 13px;
  }

  .user-role {
    font-size: 11px;
  }

  .sidebar-menu-list {
    min-height: auto;
  }

  .sidebar-menu-list .el-sub-menu__title {
    height: 48px;
    line-height: 48px;
    padding-left: 15px !important;
  }

  .sidebar-menu-list .el-menu-item {
    height: 44px;
    line-height: 44px;
    padding-left: 40px !important;
  }
}

@media (max-width: 480px) {
  .user-info {
    padding: 12px;
    gap: 8px;
  }

  .user-name {
    font-size: 12px;
  }

  .user-role {
    font-size: 10px;
  }
}
</style>
