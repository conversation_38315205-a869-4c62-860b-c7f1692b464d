import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

// 调试信息 - 检查环境变量是否正确加载
console.log('=== Supabase 配置调试 ===')
console.log('Supabase URL:', supabaseUrl)
console.log('Supabase Key (前10位):', supabaseAnonKey?.substring(0, 10))
console.log('Supabase Key (后10位):', supabaseAnonKey?.substring(-10))
console.log('Key 长度:', supabaseAnonKey?.length)
console.log('Key 类型:', supabaseAnonKey?.startsWith('eyJ') ? 'JWT Token' : 'Legacy Key')

// 解析 JWT Token 信息（如果是 JWT 格式）
if (supabaseAnonKey?.startsWith('eyJ')) {
  try {
    const payload = JSON.parse(atob(supabaseAnonKey.split('.')[1]))
    console.log('JWT Payload:', {
      iss: payload.iss,
      ref: payload.ref,
      role: payload.role,
      iat: new Date(payload.iat * 1000).toLocaleString(),
      exp: new Date(payload.exp * 1000).toLocaleString()
    })
  } catch (e) {
    console.error('JWT 解析失败:', e)
  }
}
console.log('=========================')

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Supabase 环境变量未正确配置!')
  console.error('VITE_SUPABASE_URL:', supabaseUrl)
  console.error('VITE_SUPABASE_ANON_KEY:', supabaseAnonKey ? '已设置' : '未设置')
}

// Supabase 客户端配置选项
const options = {
  db: {
    schema: 'public'
  },
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce'
  },
  global: {
    headers: {
      'X-Client-Info': 'supabase-js-web'
    }
  }
}

// 检查是否为新版 API 密钥格式
if (supabaseAnonKey && !supabaseAnonKey.startsWith('eyJ')) {
  console.warn('检测到非 JWT 格式的 API 密钥，可能需要启用 Legacy API keys')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, options)
