import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

// 验证环境变量
if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('❌ Missing Supabase environment variables. Please check your .env file.')
}

// 最新 JWT API 配置选项（基于 Supabase JS v2）
const supabaseOptions = {
  auth: {
    // 自动刷新令牌
    autoRefreshToken: true,
    // 持久化会话到本地存储
    persistSession: true,
    // 检测 URL 中的会话（OAuth 回调）
    detectSessionInUrl: true,
    // 使用 PKCE 流程提高安全性
    flowType: 'pkce',
    // 存储密钥前缀
    storageKey: 'sb-auth-token',
    // 调试模式（仅开发环境）
    debug: import.meta.env.DEV
  },
  db: {
    // 数据库 schema
    schema: 'public'
  },
  global: {
    headers: {
      'X-Client-Info': 'supabase-js-web',
      'apikey': supabaseAnonKey
    }
  },
  realtime: {
    // 实时连接配置
    params: {
      eventsPerSecond: 10
    }
  }
}

// 创建 Supabase 客户端实例
export const supabase = createClient(supabaseUrl, supabaseAnonKey, supabaseOptions)

// 调试信息（仅在开发环境）
if (import.meta.env.DEV) {
  console.log('� Supabase 客户端初始化完成')
  console.log('📍 项目 URL:', supabaseUrl)
  console.log('🔑 API Key 类型:', supabaseAnonKey.startsWith('eyJ') ? 'JWT Token (最新)' : 'Legacy Key (已弃用)')

  // 解析 JWT Token 信息
  if (supabaseAnonKey.startsWith('eyJ')) {
    try {
      const payload = JSON.parse(atob(supabaseAnonKey.split('.')[1]))
      console.log('🎫 JWT 令牌信息:', {
        发行者: payload.iss,
        项目引用: payload.ref,
        用户角色: payload.role,
        签发时间: new Date(payload.iat * 1000).toLocaleString('zh-CN'),
        过期时间: new Date(payload.exp * 1000).toLocaleString('zh-CN'),
        有效期: `${Math.floor((payload.exp - payload.iat) / (365 * 24 * 3600))} 年`
      })

      // 检查令牌是否即将过期
      const now = Math.floor(Date.now() / 1000)
      const daysUntilExpiry = Math.floor((payload.exp - now) / (24 * 3600))
      if (daysUntilExpiry < 30) {
        console.warn('⚠️ JWT 令牌将在', daysUntilExpiry, '天后过期')
      }
    } catch (e) {
      console.error('❌ JWT 解析失败:', e)
    }
  } else {
    console.warn('⚠️ 使用的是旧版 API Key，建议升级到 JWT 格式')
  }
}

// 测试 Supabase 连接
export const testSupabaseConnection = async () => {
  try {
    console.log('🔍 开始测试 Supabase 连接...')

    // 1. 测试认证服务连接
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession()

    if (sessionError) {
      console.error('❌ 认证服务连接失败:', sessionError)

      // 检查是否是 Legacy API keys 问题
      if (sessionError.message.includes('Legacy API keys are disabled')) {
        return {
          success: false,
          error: 'Legacy API keys 已禁用',
          solution: '请在 Supabase Dashboard 中启用 Legacy API keys 或使用最新的 JWT API',
          details: sessionError
        }
      }

      return {
        success: false,
        error: `认证服务错误: ${sessionError.message}`,
        details: sessionError
      }
    }

    // 2. 测试基本 API 连接
    try {
      const { error: healthError } = await supabase
        .from('pg_stat_activity')
        .select('count')
        .limit(1)
        .single()

      if (healthError && !healthError.message.includes('permission denied')) {
        console.error('❌ API 连接测试失败:', healthError)
        return {
          success: false,
          error: `API 连接错误: ${healthError.message}`,
          details: healthError
        }
      }
    } catch (apiError) {
      // 忽略权限错误，这是正常的
      if (!apiError.message.includes('permission denied')) {
        console.error('❌ API 连接异常:', apiError)
        return {
          success: false,
          error: `API 连接异常: ${apiError.message}`,
          details: apiError
        }
      }
    }

    console.log('✅ Supabase 连接测试成功')
    return {
      success: true,
      session: sessionData,
      message: '所有服务连接正常',
      timestamp: new Date().toISOString()
    }

  } catch (err) {
    console.error('❌ Supabase 连接测试异常:', err)
    return {
      success: false,
      error: `连接测试异常: ${err.message}`,
      details: err
    }
  }
}

// 认证状态监听器
export const onAuthStateChange = (callback) => {
  return supabase.auth.onAuthStateChange((event, session) => {
    console.log('🔐 认证状态变化:', event, session?.user?.email || '未登录')
    callback(event, session)
  })
}

// 获取当前用户
export const getCurrentUser = async () => {
  try {
    const { data: { user }, error } = await supabase.auth.getUser()
    if (error) throw error
    return user
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return null
  }
}
