import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

// 验证环境变量
if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env file.')
}

// 最新 JWT API 配置选项
const supabaseOptions = {
  auth: {
    // 使用最新的认证流程
    flowType: 'pkce',
    // 自动刷新令牌
    autoRefreshToken: true,
    // 持久化会话
    persistSession: true,
    // 检测 URL 中的会话
    detectSessionInUrl: true,
    // 存储密钥
    storageKey: 'sb-auth-token',
    // 调试模式
    debug: import.meta.env.DEV
  },
  db: {
    // 使用 public schema
    schema: 'public'
  },
  global: {
    headers: {
      'X-Client-Info': 'supabase-js-web'
    }
  },
  realtime: {
    // 实时连接配置
    params: {
      eventsPerSecond: 10
    }
  }
}

// 创建 Supabase 客户端
export const supabase = createClient(supabaseUrl, supabaseAnonKey, supabaseOptions)

// 调试信息（仅在开发环境）
if (import.meta.env.DEV) {
  console.log('🔧 Supabase 客户端初始化')
  console.log('📍 URL:', supabaseUrl)
  console.log('🔑 Key 类型:', supabaseAnonKey.startsWith('eyJ') ? 'JWT Token' : 'Legacy Key')

  // 解析 JWT Token 信息
  if (supabaseAnonKey.startsWith('eyJ')) {
    try {
      const payload = JSON.parse(atob(supabaseAnonKey.split('.')[1]))
      console.log('🎫 JWT 信息:', {
        发行者: payload.iss,
        项目: payload.ref,
        角色: payload.role,
        签发时间: new Date(payload.iat * 1000).toLocaleString('zh-CN'),
        过期时间: new Date(payload.exp * 1000).toLocaleString('zh-CN')
      })
    } catch (e) {
      console.error('❌ JWT 解析失败:', e)
    }
  }
}

// 测试 Supabase 连接
export const testSupabaseConnection = async () => {
  try {
    console.log('🔍 测试 Supabase 连接...')

    // 测试认证服务
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession()

    if (sessionError) {
      console.error('❌ 认证服务连接失败:', sessionError)
      return {
        success: false,
        error: `认证服务错误: ${sessionError.message}`,
        details: sessionError
      }
    }

    // 测试数据库连接（尝试查询一个系统表）
    const { error: dbError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .limit(1)

    if (dbError) {
      console.error('❌ 数据库连接失败:', dbError)
      return {
        success: false,
        error: `数据库连接错误: ${dbError.message}`,
        details: dbError
      }
    }

    console.log('✅ Supabase 连接测试成功')
    return {
      success: true,
      session: sessionData,
      database: '连接正常'
    }

  } catch (err) {
    console.error('❌ Supabase 连接测试异常:', err)
    return {
      success: false,
      error: `连接异常: ${err.message}`,
      details: err
    }
  }
}

// 认证状态监听器
export const onAuthStateChange = (callback) => {
  return supabase.auth.onAuthStateChange((event, session) => {
    console.log('🔐 认证状态变化:', event, session?.user?.email || '未登录')
    callback(event, session)
  })
}

// 获取当前用户
export const getCurrentUser = async () => {
  try {
    const { data: { user }, error } = await supabase.auth.getUser()
    if (error) throw error
    return user
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return null
  }
}
