import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

// 调试信息 - 检查环境变量是否正确加载
console.log('=== Supabase 配置调试 ===')
console.log('Supabase URL:', supabaseUrl)
console.log('Supabase Key (前10位):', supabaseAnonKey?.substring(0, 10))
console.log('Supabase Key (后10位):', supabaseAnonKey?.substring(-10))
console.log('Key 长度:', supabaseAnonKey?.length)
console.log('Key 类型:', supabaseAnonKey?.startsWith('eyJ') ? 'JWT Token' : 'Legacy Key')

// 解析 JWT Token 信息（如果是 JWT 格式）
if (supabaseAnonKey?.startsWith('eyJ')) {
  try {
    const payload = JSON.parse(atob(supabaseAnonKey.split('.')[1]))
    console.log('JWT Payload:', {
      iss: payload.iss,
      ref: payload.ref,
      role: payload.role,
      iat: new Date(payload.iat * 1000).toLocaleString(),
      exp: new Date(payload.exp * 1000).toLocaleString()
    })
  } catch (e) {
    console.error('JWT 解析失败:', e)
  }
}
console.log('=========================')

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Supabase 环境变量未正确配置!')
  console.error('VITE_SUPABASE_URL:', supabaseUrl)
  console.error('VITE_SUPABASE_ANON_KEY:', supabaseAnonKey ? '已设置' : '未设置')
}

// Supabase 客户端配置选项
const options = {
  db: {
    schema: 'public'
  },
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
    storageKey: 'supabase.auth.token'
  },
  global: {
    headers: {
      'X-Client-Info': 'supabase-js-web',
      'apikey': supabaseAnonKey,
      'Authorization': `Bearer ${supabaseAnonKey}`
    }
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
}

// 检查是否为新版 API 密钥格式
if (supabaseAnonKey && !supabaseAnonKey.startsWith('eyJ')) {
  console.warn('检测到非 JWT 格式的 API 密钥，可能需要启用 Legacy API keys')
}

// 创建 Supabase 客户端
export const supabase = createClient(supabaseUrl, supabaseAnonKey, options)

// 测试连接并提供详细错误信息
export const testSupabaseConnection = async () => {
  try {
    console.log('测试 Supabase 连接...')

    // 测试基本连接
    const { data, error } = await supabase.auth.getSession()

    if (error) {
      console.error('Supabase 连接测试失败:', error)

      if (error.message.includes('Legacy API keys are disabled')) {
        return {
          success: false,
          error: 'Legacy API keys 已禁用',
          solution: '请在 Supabase Dashboard 中启用 Legacy API keys：Settings → API → Enable legacy API keys'
        }
      } else if (error.message.includes('Invalid API key')) {
        return {
          success: false,
          error: 'API 密钥无效',
          solution: '请检查 API 密钥是否正确，或重新生成密钥'
        }
      } else {
        return {
          success: false,
          error: error.message,
          solution: '请检查 Supabase 项目配置'
        }
      }
    } else {
      console.log('Supabase 连接测试成功')
      return {
        success: true,
        data: data
      }
    }
  } catch (err) {
    console.error('Supabase 连接测试异常:', err)
    return {
      success: false,
      error: err.message,
      solution: '请检查网络连接和 Supabase 配置'
    }
  }
}
