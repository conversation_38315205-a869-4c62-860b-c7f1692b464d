import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

// 调试信息 - 检查环境变量是否正确加载
console.log('Supabase URL:', supabaseUrl)
console.log('Supabase Key (前10位):', supabaseAnonKey?.substring(0, 10))

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Supabase 环境变量未正确配置!')
  console.error('VITE_SUPABASE_URL:', supabaseUrl)
  console.error('VITE_SUPABASE_ANON_KEY:', supabaseAnonKey ? '已设置' : '未设置')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)
