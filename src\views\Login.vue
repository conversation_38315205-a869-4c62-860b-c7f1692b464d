<script setup>
import TheWelcome from '../components/TheWelcome.vue'
</script>

<template>
  <div class="login-page">
    <!-- 左侧品牌展示区域 -->
    <div class="brand-section">
      <div class="brand-content">
        <div class="logo-container">
          <div class="logo-icon">🏛️</div>
          <h1 class="brand-title">亚特兰蒂斯</h1>
          <h2 class="system-title">应付测试系统</h2>
        </div>

        <div class="brand-description">
          <p>探索数字海洋的深度</p>
          <p>构建未来的测试平台</p>
        </div>

        <!-- 装饰性元素 -->
        <div class="decorative-elements">
          <div class="wave wave-1"></div>
          <div class="wave wave-2"></div>
          <div class="wave wave-3"></div>
        </div>
      </div>
    </div>

    <!-- 右侧登录区域 -->
    <div class="login-section">
      <div class="login-wrapper">
        <TheWelcome />
      </div>
    </div>
  </div>
</template>

<style scoped>
.login-page {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

/* 左侧品牌展示区域 */
.brand-section {
  flex: 0 0 60%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

.brand-content {
  text-align: center;
  z-index: 2;
  position: relative;
  padding: 2rem;
}

.logo-container {
  margin-bottom: 3rem;
}

.logo-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  display: block;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.brand-title {
  font-size: 4rem;
  font-weight: 700;
  color: #e3f2fd;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 2px;
  background: linear-gradient(45deg, #e3f2fd, #bbdefb, #90caf9);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.system-title {
  font-size: 1.8rem;
  font-weight: 300;
  color: #bbdefb;
  margin: 1rem 0 0 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
  letter-spacing: 4px;
}

.brand-description {
  margin-top: 2rem;
}

.brand-description p {
  font-size: 1.2rem;
  color: #e1f5fe;
  margin: 0.8rem 0;
  font-weight: 300;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
  opacity: 0.9;
}

/* 装饰性波浪元素 */
.decorative-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.wave {
  position: absolute;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.wave-1 {
  top: -50%;
  left: -50%;
  animation-delay: 0s;
}

.wave-2 {
  top: -30%;
  right: -50%;
  animation-delay: 2s;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 70%);
}

.wave-3 {
  bottom: -50%;
  left: -30%;
  animation-delay: 4s;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.08) 0%, transparent 70%);
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 右侧登录区域 */
.login-section {
  flex: 0 0 40%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  position: relative;
}

.login-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(240, 248, 255, 0.8) 100%);
  z-index: 1;
}

.login-wrapper {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 400px;
  padding: 2rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .login-page {
    flex-direction: column;
  }

  .brand-section {
    flex: 0 0 40%;
    min-height: 40vh;
  }

  .brand-title {
    font-size: 2.5rem;
  }

  .system-title {
    font-size: 1.2rem;
  }

  .login-section {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .brand-section {
    flex: 0 0 35%;
  }

  .brand-title {
    font-size: 2rem;
  }

  .system-title {
    font-size: 1rem;
    letter-spacing: 2px;
  }

  .brand-description p {
    font-size: 1rem;
  }
}
</style>
