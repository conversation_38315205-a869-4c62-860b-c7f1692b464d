<script setup>
import TheWelcome from '../components/TheWelcome.vue'
</script>

<template>
  <div class="login-page">
    <!-- 左侧品牌展示区域 -->
    <div class="brand-section">
      <div class="brand-content">
        <div class="logo-container">
          <div class="logo-icon">🏛️</div>
          <h1 class="brand-title">亚特兰蒂斯</h1>
          <h2 class="system-title">应付测试系统</h2>
        </div>

        <div class="brand-description">
          <p>探索数字海洋的深度</p>
          <p>构建未来的测试平台</p>
        </div>

        <!-- 装饰性元素 -->
        <div class="decorative-elements">
          <div class="wave wave-1"></div>
          <div class="wave wave-2"></div>
          <div class="wave wave-3"></div>
        </div>
      </div>
    </div>

    <!-- 右侧登录区域 -->
    <div class="login-section">
      <div class="login-wrapper">
        <TheWelcome />
      </div>
    </div>
  </div>
</template>

<style scoped>
/* CSS 变量定义 */
:root {
  --brand-width: 60%;
  --login-width: 40%;
  --brand-title-size: 4rem;
  --system-title-size: 1.8rem;
  --brand-padding: 2rem;
  --login-padding: 2rem;
}

.login-page {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
  width: 100%;
  position: relative;
}

/* 左侧品牌展示区域 */
.brand-section {
  flex: 0 0 var(--brand-width);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  min-width: 0; /* 防止 flex 项目溢出 */
}

.brand-content {
  text-align: center;
  z-index: 2;
  position: relative;
  padding: var(--brand-padding);
  width: 100%;
  max-width: 100%;
}

.logo-container {
  margin-bottom: 3rem;
}

.logo-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  display: block;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.brand-title {
  font-size: var(--brand-title-size);
  font-weight: 700;
  color: #e3f2fd;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 2px;
  background: linear-gradient(45deg, #e3f2fd, #bbdefb, #90caf9);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  word-break: keep-all;
  white-space: nowrap;
}

.system-title {
  font-size: var(--system-title-size);
  font-weight: 300;
  color: #bbdefb;
  margin: 1rem 0 0 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
  letter-spacing: 4px;
  word-break: keep-all;
}

.brand-description {
  margin-top: 2rem;
}

.brand-description p {
  font-size: 1.2rem;
  color: #e1f5fe;
  margin: 0.8rem 0;
  font-weight: 300;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
  opacity: 0.9;
}

/* 装饰性波浪元素 */
.decorative-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.wave {
  position: absolute;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.wave-1 {
  top: -50%;
  left: -50%;
  animation-delay: 0s;
}

.wave-2 {
  top: -30%;
  right: -50%;
  animation-delay: 2s;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 70%);
}

.wave-3 {
  bottom: -50%;
  left: -30%;
  animation-delay: 4s;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.08) 0%, transparent 70%);
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 右侧登录区域 */
.login-section {
  flex: 0 0 var(--login-width);
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  position: relative;
  min-width: 0; /* 防止 flex 项目溢出 */
}

.login-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(240, 248, 255, 0.8) 100%);
  z-index: 1;
}

.login-wrapper {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 400px;
  padding: 2rem;
}

/* 响应式设计 - 针对不同电脑屏幕尺寸优化 */

/* 超大屏幕 (1920px+) */
@media (min-width: 1920px) {
  :root {
    --brand-title-size: 5rem;
    --system-title-size: 2.2rem;
    --brand-padding: 3rem;
    --login-padding: 3rem;
  }

  .brand-description p {
    font-size: 1.4rem;
  }

  .login-wrapper {
    max-width: 500px;
    padding: var(--login-padding);
  }
}

/* 大屏幕 (1440px - 1919px) */
@media (min-width: 1440px) and (max-width: 1919px) {
  :root {
    --brand-title-size: 4.5rem;
    --system-title-size: 2rem;
    --brand-padding: 2.5rem;
    --login-padding: 2.5rem;
  }

  .brand-description p {
    font-size: 1.3rem;
  }

  .login-wrapper {
    max-width: 450px;
    padding: var(--login-padding);
  }
}

/* 标准桌面 (1200px - 1439px) */
@media (min-width: 1200px) and (max-width: 1439px) {
  :root {
    --brand-width: 58%;
    --login-width: 42%;
    --brand-title-size: 3.8rem;
    --system-title-size: 1.7rem;
    --brand-padding: 2rem;
    --login-padding: 2rem;
  }

  .login-wrapper {
    max-width: 420px;
  }
}

/* 小桌面/大平板 (1024px - 1199px) */
@media (min-width: 1024px) and (max-width: 1199px) {
  :root {
    --brand-width: 55%;
    --login-width: 45%;
    --brand-title-size: 3.2rem;
    --system-title-size: 1.5rem;
    --brand-padding: 2rem;
    --login-padding: 2rem;
  }

  .brand-description p {
    font-size: 1.1rem;
  }

  .login-wrapper {
    max-width: 380px;
  }
}

/* 平板横屏 (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
  :root {
    --brand-title-size: 2.8rem;
    --system-title-size: 1.3rem;
    --brand-padding: 1.5rem;
    --login-padding: 2rem;
  }

  .login-page {
    flex-direction: column;
  }

  .brand-section {
    flex: 0 0 45vh;
    min-height: 45vh;
  }

  .login-section {
    flex: 1;
    min-height: 55vh;
  }

  .login-wrapper {
    max-width: 400px;
    padding: var(--login-padding);
  }
}

/* 平板竖屏/大手机 (480px - 767px) */
@media (min-width: 480px) and (max-width: 767px) {
  .login-page {
    flex-direction: column;
  }

  .brand-section {
    flex: 0 0 40vh;
    min-height: 40vh;
  }

  .login-section {
    flex: 1;
    min-height: 60vh;
  }

  .brand-title {
    font-size: 2.2rem;
  }

  .system-title {
    font-size: 1.1rem;
    letter-spacing: 2px;
  }

  .brand-description p {
    font-size: 1rem;
  }

  .login-wrapper {
    padding: 1.5rem;
  }
}

/* 小手机 (最大479px) */
@media (max-width: 479px) {
  .login-page {
    flex-direction: column;
  }

  .brand-section {
    flex: 0 0 35vh;
    min-height: 35vh;
  }

  .login-section {
    flex: 1;
    min-height: 65vh;
  }

  .brand-title {
    font-size: 1.8rem;
  }

  .system-title {
    font-size: 0.9rem;
    letter-spacing: 1px;
  }

  .brand-description p {
    font-size: 0.9rem;
  }

  .login-wrapper {
    padding: 1rem;
  }

  .logo-icon {
    font-size: 3rem;
  }
}

/* 确保在所有屏幕上都有合适的最小高度 */
@media (min-height: 800px) {
  .login-page {
    min-height: 100vh;
  }
}

@media (max-height: 600px) and (min-width: 1024px) {
  .brand-title {
    font-size: 3rem;
  }

  .system-title {
    font-size: 1.4rem;
  }

  .brand-description {
    margin-top: 1rem;
  }

  .brand-description p {
    margin: 0.5rem 0;
  }
}
</style>
