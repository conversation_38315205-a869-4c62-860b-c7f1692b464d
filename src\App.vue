<script setup>
import HelloWorld from './components/HelloWorld.vue'
import TheWelcome from './components/TheWelcome.vue'
import { useRoute } from 'vue-router'

const route = useRoute()
</script>

<template>
  <!-- 只在非路由页面显示主页内容 -->
  <div v-if="route.path === '/'">
    <header>
      <div class="wrapper">
        <HelloWorld msg="亚特兰蒂斯" />
      </div>
    </header>

    <main>
      <TheWelcome />
    </main>
  </div>

  <!-- 路由视图 -->
  <router-view v-else />
</template>

<style scoped>
header {
  line-height: 1.5;
}

.logo {
  display: block;
  margin: 0 auto 2rem;
}

@media (min-width: 1024px) {
  header {
    display: flex;
    place-items: center;
    padding-right: calc(var(--section-gap) / 2);
  }

  .logo {
    margin: 0 2rem 0 0;
  }

  header .wrapper {
    display: flex;
    place-items: flex-start;
    flex-wrap: wrap;
  }
}
</style>
