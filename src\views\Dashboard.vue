<script setup>
import { ref } from 'vue'
import SidebarMenu from '../components/SidebarMenu.vue'
import ContentArea from '../components/ContentArea.vue'

// 当前选中的菜单项
const activeMenuItem = ref('user-management')

// 处理菜单选择
const handleMenuSelect = (menuKey) => {
  activeMenuItem.value = menuKey
  console.log('选中菜单:', menuKey)
}
</script>

<template>
  <div class="dashboard-container">
    <!-- 左侧菜单栏 -->
    <div class="sidebar">
      <SidebarMenu
        :active-menu="activeMenuItem"
        @menu-select="handleMenuSelect"
      />
    </div>

    <!-- 右侧内容区域 -->
    <div class="content">
      <ContentArea :active-menu="activeMenuItem" />
    </div>
  </div>
</template>

<style scoped>
.dashboard-container {
  display: flex;
  height: 100vh;
  width: 100vw;
  background-color: #f5f7fa;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
}

.sidebar {
  width: 280px;
  min-width: 280px;
  background-color: #ffffff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  z-index: 100;
  height: 100vh;
}

.content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #f5f7fa;
  height: 100vh;
  width: calc(100vw - 280px);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .sidebar {
    width: 260px;
    min-width: 260px;
  }

  .content {
    width: calc(100vw - 260px);
    padding: 15px;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    flex-direction: column;
    position: relative;
  }

  .sidebar {
    width: 100%;
    min-width: 100%;
    height: auto;
    max-height: 200px;
    position: relative;
  }

  .content {
    width: 100%;
    height: calc(100vh - 200px);
    padding: 10px;
  }
}

@media (max-width: 480px) {
  .content {
    padding: 8px;
  }
}
</style>
