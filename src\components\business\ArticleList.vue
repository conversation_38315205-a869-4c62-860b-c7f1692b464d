<script setup>
import { ref, onMounted } from 'vue'
import { supabase } from '../../supabaseClient.js'

// 响应式数据
const oaData = ref([])
const loading = ref(false)
const searchText = ref('')
const errorMessage = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 查询 OA 数据 - 使用最新的 Supabase 查询方法
const fetchOaData = async () => {
  loading.value = true
  errorMessage.value = ''

  try {
    console.log('🔍 开始查询 oastore 表数据...')

    // 基础查询 - 读取所有行和所有列
    let query = supabase.from('oastore').select('*', { count: 'exact' })

    // 如果有搜索条件，添加搜索过滤
    if (searchText.value.trim()) {
      console.log('🔎 应用搜索过滤:', searchText.value)
      // 在多个字段中搜索
      query = query.or(`title.ilike.%${searchText.value}%,content.ilike.%${searchText.value}%,description.ilike.%${searchText.value}%`)
    }

    // 添加排序 - 按创建时间倒序
    query = query.order('created_at', { ascending: false })

    // 分页处理
    if (pageSize.value > 0) {
      const from = (currentPage.value - 1) * pageSize.value
      const to = from + pageSize.value - 1
      query = query.range(from, to)
      console.log(`📄 分页查询: ${from}-${to}`)
    }

    // 执行查询
    const { data: oastore, error, count } = await query

    if (error) {
      console.error('❌ Supabase 查询错误:', error)

      // 根据错误类型提供更具体的错误信息
      if (error.message.includes('relation') || error.message.includes('table')) {
        errorMessage.value = '❌ 表不存在：请确认 oastore 表已在数据库中创建'
      } else if (error.message.includes('permission') || error.message.includes('RLS')) {
        errorMessage.value = '❌ 权限错误：请检查行级安全策略(RLS)或 API Key 权限'
      } else if (error.message.includes('column')) {
        errorMessage.value = '❌ 列不存在：请检查表结构是否正确'
      } else {
        errorMessage.value = `❌ 查询错误: ${error.message}`
      }

      // 清空数据
      oaData.value = []
      total.value = 0
    } else {
      // 查询成功
      oaData.value = oastore || []
      total.value = count || 0

      console.log(`✅ 查询成功! 获取到 ${oastore?.length || 0} 条记录，总计 ${count || 0} 条`)

      // 如果没有数据，显示友好提示
      if (!oastore || oastore.length === 0) {
        if (searchText.value.trim()) {
          errorMessage.value = '🔍 未找到匹配的搜索结果，请尝试其他关键词'
        } else {
          errorMessage.value = '📝 oastore 表中暂无数据，请先添加一些数据'
        }
      } else {
        errorMessage.value = '' // 清除错误信息
      }
    }
  } catch (err) {
    console.error('❌ 查询异常:', err)

    // 网络或其他异常处理
    if (err.message.includes('fetch') || err.message.includes('network')) {
      errorMessage.value = '🌐 网络连接错误：请检查网络连接和 Supabase 配置'
    } else if (err.message.includes('unauthorized') || err.message.includes('401')) {
      errorMessage.value = '🔐 认证错误：请检查 Supabase API Key 是否正确'
    } else {
      errorMessage.value = `⚠️ 查询异常: ${err.message}`
    }

    // 清空数据
    oaData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchOaData()
}

// 重置搜索
const handleReset = () => {
  searchText.value = ''
  currentPage.value = 1
  fetchOaData()
}

// 刷新数据
const handleRefresh = () => {
  fetchOaData()
}

// 分页变化
const handlePageChange = (page) => {
  currentPage.value = page
  fetchOaData()
}

// 页面大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchOaData()
}

// 查看详情
const handleView = (row) => {
  console.log('查看详情:', row)
  // 这里可以添加查看详情的逻辑
}

// 编辑
const handleEdit = (row) => {
  console.log('编辑:', row)
  // 这里可以添加编辑的逻辑
}

// 删除记录
const handleDelete = async (row) => {
  try {
    console.log('🗑️ 删除记录:', row.id)

    // 使用 Supabase 删除方法
    const { error } = await supabase
      .from('oastore')
      .delete()
      .eq('id', row.id)

    if (error) {
      console.error('❌ 删除错误:', error)
      errorMessage.value = `删除失败: ${error.message}`
    } else {
      console.log('✅ 删除成功')
      errorMessage.value = '' // 清除错误信息

      // 重新加载数据以更新表格
      await fetchOaData()

      // 显示成功提示（可选）
      console.log(`✅ 已删除记录 ID: ${row.id}`)
    }
  } catch (err) {
    console.error('❌ 删除异常:', err)
    errorMessage.value = '删除操作失败，请稍后重试'
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 测试 Supabase 连接
const testConnection = async () => {
  loading.value = true
  errorMessage.value = ''

  try {
    console.log('测试 Supabase 连接...')

    // 首先测试基本连接
    const { error } = await supabase
      .from('oastore')
      .select('count', { count: 'exact', head: true })

    if (error) {
      console.error('连接测试失败:', error)
      errorMessage.value = `连接测试失败: ${error.message}`
    } else {
      console.log('连接测试成功')
      // 连接成功后再查询数据
      fetchOaData()
    }
  } catch (err) {
    console.error('连接测试异常:', err)
    errorMessage.value = `连接测试异常: ${err.message}`
  } finally {
    loading.value = false
  }
}

// 组件挂载时先测试连接
onMounted(() => {
  testConnection()
})
</script>

<template>
  <div class="article-list">
    <!-- 页面标题 -->
    <div class="page-header">
      <h3>OA 数据管理</h3>
      <p>管理和查看 OA 系统中的所有数据</p>
    </div>

    <!-- 错误提示 -->
    <el-alert
      v-if="errorMessage"
      :title="errorMessage"
      type="error"
      show-icon
      closable
      @close="errorMessage = ''"
      style="margin-bottom: 20px"
    />

    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchText"
            placeholder="搜索标题或内容"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="handleSearch" :icon="Search">
            搜索
          </el-button>
          <el-button @click="handleReset" :icon="Refresh">
            重置
          </el-button>
          <el-button @click="handleRefresh" :icon="RefreshRight">
            刷新
          </el-button>
          <el-button @click="testConnection" :icon="Connection" type="success">
            测试连接
          </el-button>
        </el-col>
        <el-col :span="8" class="text-right">
          <div class="data-stats">
            <el-tag type="info" class="stats-tag">
              📊 共 {{ total }} 条记录
            </el-tag>
            <el-tag v-if="oaData.length > 0" type="success" class="stats-tag">
              📄 当前页 {{ oaData.length }} 条
            </el-tag>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="oaData"
        v-loading="loading"
        stripe
        style="width: 100%"
        height="100%"
        empty-text="暂无数据"
      >
        <!-- ID 列 -->
        <el-table-column prop="id" label="ID" width="80" sortable align="center" />

        <!-- 标题列 -->
        <el-table-column prop="title" label="标题" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <span :title="row.title">{{ row.title || '-' }}</span>
          </template>
        </el-table-column>

        <!-- 内容列 -->
        <el-table-column prop="content" label="内容" min-width="300" show-overflow-tooltip>
          <template #default="{ row }">
            <span :title="row.content">{{ row.content || '-' }}</span>
          </template>
        </el-table-column>

        <!-- 描述列（如果存在） -->
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <span :title="row.description">{{ row.description || '-' }}</span>
          </template>
        </el-table-column>

        <!-- 状态列 -->
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag
              :type="row.status === 'active' ? 'success' :
                     row.status === 'pending' ? 'warning' :
                     row.status === 'inactive' ? 'danger' : 'info'"
            >
              {{ row.status || '未设置' }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 创建时间列 -->
        <el-table-column prop="created_at" label="创建时间" width="180" sortable>
          <template #default="{ row }">
            <span :title="formatDate(row.created_at)">
              {{ formatDate(row.created_at) }}
            </span>
          </template>
        </el-table-column>

        <!-- 更新时间列 -->
        <el-table-column prop="updated_at" label="更新时间" width="180" sortable>
          <template #default="{ row }">
            <span :title="formatDate(row.updated_at)">
              {{ formatDate(row.updated_at) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleView(row)"
              :icon="View"
            >
              查看
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="handleEdit(row)"
              :icon="Edit"
            >
              编辑
            </el-button>
            <el-popconfirm
              title="确定要删除这条记录吗？"
              @confirm="handleDelete(row)"
            >
              <template #reference>
                <el-button
                  type="danger"
                  size="small"
                  :icon="Delete"
                >
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<style scoped>
.article-list {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.page-header {
  margin-bottom: 20px;
  padding: 0 4px;
  flex-shrink: 0;
}

.page-header h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
  flex-shrink: 0;
}

.text-right {
  text-align: right;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.data-stats {
  display: flex;
  gap: 8px;
  align-items: center;
}

.stats-tag {
  font-size: 12px;
}

.table-container {
  flex: 1;
  margin-bottom: 20px;
  overflow: hidden;
  min-height: 0;
}

.table-container .el-table {
  height: 100% !important;
}

.pagination {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  flex-shrink: 0;
  background: #fff;
  border-top: 1px solid #e4e7ed;
  margin: 0 -24px -24px -24px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .search-bar {
    padding: 16px;
    margin-bottom: 16px;
  }

  .pagination {
    padding: 16px 0;
  }
}

@media (max-width: 768px) {
  .page-header {
    margin-bottom: 15px;
  }

  .page-header h3 {
    font-size: 18px;
  }

  .search-bar {
    padding: 12px;
    margin-bottom: 12px;
  }

  .search-bar .el-col {
    margin-bottom: 10px;
  }

  .text-right {
    text-align: left;
    justify-content: flex-start;
  }

  .pagination {
    overflow-x: auto;
    padding: 12px 0;
  }

  .table-container {
    margin-bottom: 12px;
  }
}

@media (max-width: 480px) {
  .page-header h3 {
    font-size: 16px;
  }

  .page-header p {
    font-size: 13px;
  }

  .search-bar {
    padding: 8px;
  }

  .pagination {
    padding: 8px 0;
  }
}
</style>
