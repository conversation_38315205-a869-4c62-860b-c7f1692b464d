<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { supabase } from '../supabaseClient.js'

const router = useRouter()

// 响应式数据
const email = ref('')
const password = ref('')
const loading = ref(false)
const errorMessage = ref('')
const successMessage = ref('')
const user = ref(null)

// 检查用户登录状态
const checkUser = async () => {
  const { data: { user: currentUser } } = await supabase.auth.getUser()
  user.value = currentUser
}

// 测试 API 密钥有效性
const testApiKey = async () => {
  try {
    console.log('测试 Supabase API 密钥...')
    const { data, error } = await supabase.auth.getSession()

    if (error) {
      console.error('API 密钥测试失败:', error)
      errorMessage.value = `API 密钥无效: ${error.message}`
    } else {
      console.log('API 密钥测试成功:', data)
    }
  } catch (err) {
    console.error('API 密钥测试异常:', err)
    errorMessage.value = `API 密钥测试异常: ${err.message}`
  }
}

// 组件挂载时检查用户状态和测试 API 密钥
checkUser()
testApiKey()

// Supabase 登录方法
const handleLogin = async () => {
  if (!email.value || !password.value) {
    errorMessage.value = '请输入邮箱和密码'
    return
  }

  loading.value = true
  errorMessage.value = ''
  successMessage.value = ''

  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: email.value,
      password: password.value,
    })

    if (error) {
      errorMessage.value = error.message
      console.error('登录错误:', error)
    } else {
      successMessage.value = `欢迎登录，${data.user.email}！`
      console.log('登录成功:', data)
      user.value = data.user

      // 清空表单
      email.value = ''
      password.value = ''

      // 登录成功后跳转到 dashboard 页面
      setTimeout(() => {
        router.push('/dashboard')
      }, 1500) // 1.5秒后跳转，让用户看到成功消息
    }
  } catch (err) {
    errorMessage.value = '登录失败，请稍后重试'
    console.error('登录异常:', err)
  } finally {
    loading.value = false
  }
}

// Supabase 注册方法
const handleRegister = async () => {
  if (!email.value || !password.value) {
    errorMessage.value = '请输入邮箱和密码'
    return
  }

  if (password.value.length < 6) {
    errorMessage.value = '密码长度至少需要6位'
    return
  }

  loading.value = true
  errorMessage.value = ''
  successMessage.value = ''

  try {
    const { data, error } = await supabase.auth.signUp({
      email: email.value,
      password: password.value,
    })

    if (error) {
      errorMessage.value = error.message
      console.error('注册错误:', error)
    } else {
      successMessage.value = '注册成功！请检查您的邮箱以验证账户。'
      console.log('注册成功:', data)

      // 清空表单
      email.value = ''
      password.value = ''
    }
  } catch (err) {
    errorMessage.value = '注册失败，请稍后重试'
    console.error('注册异常:', err)
  } finally {
    loading.value = false
  }
}

// 登出方法
const handleLogout = async () => {
  loading.value = true
  errorMessage.value = ''
  successMessage.value = ''

  try {
    const { error } = await supabase.auth.signOut()

    if (error) {
      errorMessage.value = error.message
      console.error('登出错误:', error)
    } else {
      user.value = null
      successMessage.value = '已成功登出'
      console.log('登出成功')
    }
  } catch (err) {
    errorMessage.value = '登出失败，请稍后重试'
    console.error('登出异常:', err)
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="login-container">
    <div class="login-form">
      <!-- 已登录用户显示 -->
      <div v-if="user" class="user-info">
        <h2 class="login-title">欢迎回来</h2>
        <div class="user-details">
          <p><strong>邮箱：</strong>{{ user.email }}</p>
          <p><strong>用户ID：</strong>{{ user.id }}</p>
          <p><strong>最后登录：</strong>{{ new Date(user.last_sign_in_at).toLocaleString() }}</p>
        </div>

        <!-- 成功消息 -->
        <div v-if="successMessage" class="message success-message">
          {{ successMessage }}
        </div>

        <!-- 错误消息 -->
        <div v-if="errorMessage" class="message error-message">
          {{ errorMessage }}
        </div>

        <button
          class="logout-button"
          @click="handleLogout"
          :disabled="loading"
        >
          {{ loading ? '登出中...' : '登出' }}
        </button>
      </div>

      <!-- 未登录用户显示登录表单 -->
      <div v-else>
        <h2 class="login-title">用户登录</h2>

        <!-- 错误消息 -->
        <div v-if="errorMessage" class="message error-message">
          {{ errorMessage }}
        </div>

        <!-- 成功消息 -->
        <div v-if="successMessage" class="message success-message">
          {{ successMessage }}
        </div>

        <form @submit.prevent="handleLogin">
          <div class="form-group">
            <label for="email">邮箱</label>
            <input
              id="email"
              v-model="email"
              type="email"
              placeholder="请输入邮箱地址"
              required
              :disabled="loading"
            />
          </div>

          <div class="form-group">
            <label for="password">密码</label>
            <input
              id="password"
              v-model="password"
              type="password"
              placeholder="请输入密码（至少6位）"
              required
              :disabled="loading"
            />
          </div>

          <div class="button-group">
            <button
              type="submit"
              class="login-button"
              :disabled="loading"
            >
              {{ loading ? '登录中...' : '登录' }}
            </button>

            <button
              type="button"
              class="register-button"
              @click="handleRegister"
              :disabled="loading"
            >
              {{ loading ? '注册中...' : '注册' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100%;
  padding: 0;
}

.login-form {
  background: rgba(255, 255, 255, 0.95);
  padding: 2.5rem;
  border-radius: 20px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 10px 20px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  width: 100%;
  max-width: 100%;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.login-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

.login-title {
  text-align: center;
  margin-bottom: 2rem;
  color: #2c3e50;
  font-size: 2rem;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.login-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: #555;
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 1rem 1.2rem;
  border: 2px solid rgba(102, 126, 234, 0.2);
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow:
    0 0 0 3px rgba(102, 126, 234, 0.1),
    0 4px 12px rgba(102, 126, 234, 0.15);
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
}

.form-group input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 消息样式 */
.message {
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 14px;
  text-align: center;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  border: 1px solid #ef9a9a;
}

.success-message {
  background-color: #e8f5e8;
  color: #2e7d32;
  border: 1px solid #81c784;
}

/* 按钮组样式 */
.button-group {
  display: flex;
  gap: 12px;
}

.login-button,
.register-button {
  flex: 1;
  padding: 1rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

.login-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow:
    0 8px 16px rgba(102, 126, 234, 0.3),
    0 4px 8px rgba(102, 126, 234, 0.2);
}

.login-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  box-shadow:
    0 12px 24px rgba(102, 126, 234, 0.4),
    0 6px 12px rgba(102, 126, 234, 0.3);
  transform: translateY(-3px);
}

.login-button:active:not(:disabled) {
  transform: translateY(-1px);
  box-shadow:
    0 6px 12px rgba(102, 126, 234, 0.3),
    0 3px 6px rgba(102, 126, 234, 0.2);
}

.register-button {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  box-shadow:
    0 8px 16px rgba(240, 147, 251, 0.3),
    0 4px 8px rgba(240, 147, 251, 0.2);
}

.register-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #ee82f0 0%, #f34a5f 100%);
  box-shadow:
    0 12px 24px rgba(240, 147, 251, 0.4),
    0 6px 12px rgba(240, 147, 251, 0.3);
  transform: translateY(-3px);
}

.register-button:active:not(:disabled) {
  transform: translateY(-1px);
  box-shadow:
    0 6px 12px rgba(240, 147, 251, 0.3),
    0 3px 6px rgba(240, 147, 251, 0.2);
}

.login-button:disabled,
.register-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 用户信息样式 */
.user-info {
  text-align: center;
}

.user-details {
  background: rgba(255, 255, 255, 0.8);
  padding: 20px;
  border-radius: 6px;
  margin: 20px 0;
  text-align: left;
}

.user-details p {
  margin: 8px 0;
  color: #555;
  font-size: 14px;
}

.user-details strong {
  color: #333;
}

.logout-button {
  width: 100%;
  padding: 12px;
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(244, 67, 54, 0.3);
}

.logout-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #d32f2f 0%, #c62828 100%);
  box-shadow: 0 4px 8px rgba(244, 67, 54, 0.4);
  transform: translateY(-1px);
}

.logout-button:active:not(:disabled) {
  background: linear-gradient(135deg, #c62828 0%, #b71c1c 100%);
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(244, 67, 54, 0.3);
}

.logout-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 响应式设计优化 */

/* 超大屏幕优化 */
@media (min-width: 1920px) {
  .login-form {
    padding: 3.5rem;
    border-radius: 24px;
  }

  .login-title {
    font-size: 2.5rem;
    margin-bottom: 2.5rem;
  }

  .form-group {
    margin-bottom: 2rem;
  }

  .form-group label {
    font-size: 1.1rem;
    margin-bottom: 0.8rem;
  }

  .form-group input {
    padding: 1.3rem 1.5rem;
    font-size: 1.1rem;
    border-radius: 14px;
  }

  .login-button,
  .register-button {
    padding: 1.3rem 2rem;
    font-size: 1.1rem;
    border-radius: 14px;
  }

  .button-group {
    gap: 1.5rem;
  }
}

/* 大屏幕优化 */
@media (min-width: 1440px) and (max-width: 1919px) {
  .login-form {
    padding: 3rem;
    border-radius: 22px;
  }

  .login-title {
    font-size: 2.3rem;
  }

  .form-group input {
    padding: 1.2rem 1.4rem;
    font-size: 1.05rem;
  }

  .login-button,
  .register-button {
    padding: 1.2rem 1.8rem;
    font-size: 1.05rem;
  }
}

/* 标准桌面优化 */
@media (min-width: 1200px) and (max-width: 1439px) {
  .login-form {
    padding: 2.8rem;
  }

  .login-title {
    font-size: 2.1rem;
  }
}

/* 小桌面优化 */
@media (min-width: 1024px) and (max-width: 1199px) {
  .login-form {
    padding: 2.3rem;
    border-radius: 18px;
  }

  .login-title {
    font-size: 1.9rem;
  }

  .form-group input {
    padding: 0.9rem 1.1rem;
  }

  .login-button,
  .register-button {
    padding: 0.9rem 1.3rem;
  }
}

/* 平板横屏优化 */
@media (min-width: 768px) and (max-width: 1023px) {
  .login-container {
    padding: 1rem;
  }

  .login-form {
    padding: 2rem;
    border-radius: 16px;
  }

  .login-title {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
  }

  .form-group {
    margin-bottom: 1.2rem;
  }

  .button-group {
    flex-direction: column;
    gap: 1rem;
  }

  .login-button,
  .register-button {
    width: 100%;
  }
}

/* 平板竖屏/大手机优化 */
@media (min-width: 480px) and (max-width: 767px) {
  .login-form {
    padding: 1.8rem;
    border-radius: 14px;
  }

  .login-title {
    font-size: 1.6rem;
  }

  .form-group input {
    padding: 0.8rem 1rem;
    font-size: 15px;
  }

  .login-button,
  .register-button {
    padding: 0.8rem 1.2rem;
    font-size: 15px;
  }
}

/* 小手机优化 */
@media (max-width: 479px) {
  .login-container {
    padding: 0.5rem;
  }

  .login-form {
    padding: 1.5rem;
    border-radius: 12px;
  }

  .login-title {
    font-size: 1.4rem;
    margin-bottom: 1.2rem;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  .form-group label {
    font-size: 14px;
  }

  .form-group input {
    padding: 0.7rem 0.9rem;
    font-size: 14px;
    border-radius: 10px;
  }

  .login-button,
  .register-button {
    padding: 0.7rem 1rem;
    font-size: 14px;
    border-radius: 10px;
  }

  .message {
    padding: 0.8rem;
    font-size: 13px;
  }
}

/* 矮屏幕优化 */
@media (max-height: 600px) and (min-width: 1024px) {
  .login-form {
    padding: 2rem;
  }

  .login-title {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  .form-group input {
    padding: 0.8rem 1rem;
  }

  .login-button,
  .register-button {
    padding: 0.8rem 1.2rem;
  }
}

/* 确保用户信息区域也响应式 */
.user-details {
  font-size: clamp(12px, 2vw, 14px);
}

.user-details p {
  margin: clamp(6px, 1vw, 8px) 0;
}

/* 使用 clamp 确保字体大小在合理范围内 */
.login-title {
  font-size: clamp(1.4rem, 4vw, 2.5rem);
}

.form-group label {
  font-size: clamp(14px, 2vw, 16px);
}

.form-group input {
  font-size: clamp(14px, 2vw, 16px);
}

.login-button,
.register-button {
  font-size: clamp(14px, 2vw, 16px);
}
</style>

