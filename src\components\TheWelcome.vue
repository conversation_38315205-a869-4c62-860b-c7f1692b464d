<script setup>
import { ref } from 'vue'
import { supabase } from '../supabaseClient.js'

// 响应式数据
const email = ref('')
const password = ref('')
const loading = ref(false)
const errorMessage = ref('')
const successMessage = ref('')

// 登录方法
const handleLogin = async () => {
  if (!email.value || !password.value) {
    errorMessage.value = '请输入邮箱和密码'
    return
  }

  loading.value = true
  errorMessage.value = ''
  successMessage.value = ''

  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: email.value,
      password: password.value,
    })

    if (error) {
      errorMessage.value = error.message
    } else {
      successMessage.value = `欢迎登录，${data.user.email}！`
      console.log('登录成功:', data)

      // 可以在这里添加登录成功后的逻辑，比如跳转页面
      // window.location.href = '/dashboard'
    }
  } catch (err) {
    errorMessage.value = '登录失败，请稍后重试'
    console.error('登录错误:', err)
  } finally {
    loading.value = false
  }
}

// 注册方法
const handleRegister = async () => {
  if (!email.value || !password.value) {
    errorMessage.value = '请输入邮箱和密码'
    return
  }

  loading.value = true
  errorMessage.value = ''
  successMessage.value = ''

  try {
    const { data, error } = await supabase.auth.signUp({
      email: email.value,
      password: password.value,
    })

    if (error) {
      errorMessage.value = error.message
    } else {
      successMessage.value = '注册成功！请检查您的邮箱以验证账户。'
      console.log('注册成功:', data)
    }
  } catch (err) {
    errorMessage.value = '注册失败，请稍后重试'
    console.error('注册错误:', err)
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="login-container">
    <div class="login-form">
      <h2 class="login-title">用户登录</h2>

      <!-- 错误消息 -->
      <div v-if="errorMessage" class="message error-message">
        {{ errorMessage }}
      </div>

      <!-- 成功消息 -->
      <div v-if="successMessage" class="message success-message">
        {{ successMessage }}
      </div>

      <form @submit.prevent="handleLogin">
        <div class="form-group">
          <label for="email">邮箱</label>
          <input
            id="email"
            v-model="email"
            type="email"
            placeholder="请输入邮箱地址"
            required
            :disabled="loading"
          />
        </div>

        <div class="form-group">
          <label for="password">密码</label>
          <input
            id="password"
            v-model="password"
            type="password"
            placeholder="请输入密码"
            required
            :disabled="loading"
          />
        </div>

        <div class="button-group">
          <button
            type="submit"
            class="login-button"
            :disabled="loading"
          >
            {{ loading ? '登录中...' : '登录' }}
          </button>

          <button
            type="button"
            class="register-button"
            @click="handleRegister"
            :disabled="loading"
          >
            {{ loading ? '注册中...' : '注册' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 20px;
}

.login-form {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
  border: 1px solid #90caf9;
}

.login-title {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
  font-size: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: #555;
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 12px;
  border: 1px solid #90caf9;
  border-radius: 4px;
  font-size: 16px;
  transition: border-color 0.3s;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.9);
}

.form-group input:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.25);
  background: rgba(255, 255, 255, 1);
}

.form-group input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 消息样式 */
.message {
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 14px;
  text-align: center;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  border: 1px solid #ef9a9a;
}

.success-message {
  background-color: #e8f5e8;
  color: #2e7d32;
  border: 1px solid #81c784;
}

/* 按钮组样式 */
.button-group {
  display: flex;
  gap: 12px;
}

.login-button,
.register-button {
  flex: 1;
  padding: 12px;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s;
  font-weight: 500;
}

.login-button {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
}

.login-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.4);
  transform: translateY(-1px);
}

.login-button:active:not(:disabled) {
  background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
}

.register-button {
  background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

.register-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #388e3c 0%, #2e7d32 100%);
  box-shadow: 0 4px 8px rgba(76, 175, 80, 0.4);
  transform: translateY(-1px);
}

.register-button:active:not(:disabled) {
  background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

.login-button:disabled,
.register-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}
</style>
