<script setup>
import { ref } from 'vue'

// 响应式数据
const username = ref('')
const password = ref('')

// 登录方法
const handleLogin = () => {
  if (!username.value || !password.value) {
    alert('请输入账号和密码')
    return
  }

  console.log('登录信息:', {
    username: username.value,
    password: password.value
  })

  // 这里可以添加实际的登录逻辑
  alert(`欢迎登录，${username.value}！`)
}
</script>

<template>
  <div class="login-container">
    <div class="login-form">
      <h2 class="login-title">用户登录</h2>

      <form @submit.prevent="handleLogin">
        <div class="form-group">
          <label for="username">账号</label>
          <input
            id="username"
            v-model="username"
            type="text"
            placeholder="请输入账号"
            required
          />
        </div>

        <div class="form-group">
          <label for="password">密码</label>
          <input
            id="password"
            v-model="password"
            type="password"
            placeholder="请输入密码"
            required
          />
        </div>

        <button type="submit" class="login-button">登录</button>
      </form>
    </div>
  </div>
</template>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 20px;
}

.login-form {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
  border: 1px solid #90caf9;
}

.login-title {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
  font-size: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: #555;
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 12px;
  border: 1px solid #90caf9;
  border-radius: 4px;
  font-size: 16px;
  transition: border-color 0.3s;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.9);
}

.form-group input:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.25);
  background: rgba(255, 255, 255, 1);
}

.login-button {
  width: 100%;
  padding: 12px;
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
}

.login-button:hover {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.4);
  transform: translateY(-1px);
}

.login-button:active {
  background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
}
</style>
