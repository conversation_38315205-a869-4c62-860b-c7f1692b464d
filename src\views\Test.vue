<script setup>
import { useAuth } from '../composables/useAuth.js'
import { useRouter } from 'vue-router'

const { user, logout } = useAuth()
const router = useRouter()

// 登出并跳转到登录页
const handleLogout = async () => {
  await logout()
  router.push('/login')
}
</script>

<template>
  <div class="test-container">
    <div class="test-content">
      <h1 class="test-title">🎉 欢迎进入测试页面</h1>
      
      <!-- 用户信息 -->
      <div v-if="user" class="user-info">
        <h3>用户信息</h3>
        <p><strong>邮箱：</strong>{{ user.email }}</p>
        <p><strong>用户ID：</strong>{{ user.id }}</p>
        <p><strong>最后登录：</strong>{{ new Date(user.last_sign_in_at).toLocaleString() }}</p>
      </div>
      
      <div class="description">
        <p>🔒 这是一个需要登录才能访问的受保护页面</p>
        <p>只有通过身份验证的用户才能看到此内容</p>
      </div>
      
      <div class="button-group">
        <button @click="handleLogout" class="logout-button">
          🚪 安全登出
        </button>
        
        <router-link to="/login" class="back-link">
          🏠 返回登录页
        </router-link>
      </div>
    </div>
  </div>
</template>

<style scoped>
.test-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.test-content {
  text-align: center;
  background: white;
  padding: 60px 40px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 100%;
}

.test-title {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 30px;
  font-weight: 600;
}

.user-info {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
  text-align: left;
  border: 1px solid #90caf9;
}

.user-info h3 {
  margin-top: 0;
  color: #1976d2;
  text-align: center;
}

.user-info p {
  margin: 8px 0;
  color: #555;
  font-size: 14px;
}

.user-info strong {
  color: #333;
}

.description {
  margin: 30px 0;
}

.description p {
  font-size: 1.1rem;
  color: #666;
  margin: 10px 0;
  line-height: 1.6;
}

.button-group {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.logout-button,
.back-link {
  display: inline-block;
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 16px;
  cursor: pointer;
  border: none;
}

.logout-button {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

.logout-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
  background: linear-gradient(135deg, #d32f2f 0%, #c62828 100%);
}

.back-link {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.back-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
}
</style>
