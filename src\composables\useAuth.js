import { ref, computed } from 'vue'
import { supabase, onAuthStateChange } from '../supabaseClient.js'

// 全局状态
const user = ref(null)
const loading = ref(true)
const initialized = ref(false)

export function useAuth() {
  // 初始化认证状态
  const initAuth = async () => {
    if (initialized.value) return

    try {
      loading.value = true
      console.log('🔄 初始化认证状态...')

      // 获取当前会话
      const { data: { session }, error } = await supabase.auth.getSession()

      if (error) {
        console.error('❌ 获取会话失败:', error)
        user.value = null
      } else {
        user.value = session?.user || null
        console.log('👤 当前用户:', user.value?.email || '未登录')
      }

      initialized.value = true
    } catch (error) {
      console.error('❌ 认证初始化异常:', error)
      user.value = null
    } finally {
      loading.value = false
    }
  }

  // 检查用户认证状态
  const checkAuth = async () => {
    if (!initialized.value) {
      await initAuth()
    }
    return user.value
  }

  // 登录
  const login = async (email, password) => {
    try {
      loading.value = true
      console.log('🔐 尝试登录:', email)

      const { data, error } = await supabase.auth.signInWithPassword({
        email: email.trim(),
        password
      })

      if (error) {
        console.error('❌ 登录失败:', error)
        throw error
      }

      user.value = data.user
      console.log('✅ 登录成功:', user.value.email)
      return { success: true, user: data.user, session: data.session }
    } catch (error) {
      console.error('❌ 登录异常:', error)
      return {
        success: false,
        error: error.message,
        code: error.code || 'UNKNOWN_ERROR'
      }
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (email, password) => {
    try {
      loading.value = true
      console.log('📝 尝试注册:', email)

      const { data, error } = await supabase.auth.signUp({
        email: email.trim(),
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/dashboard`
        }
      })

      if (error) {
        console.error('❌ 注册失败:', error)
        throw error
      }

      // 检查是否需要邮箱验证
      if (data.user && !data.session) {
        console.log('📧 需要邮箱验证')
        return {
          success: true,
          user: data.user,
          needsVerification: true,
          message: '注册成功！请检查您的邮箱并点击验证链接。'
        }
      }

      user.value = data.user
      console.log('✅ 注册成功:', user.value?.email)
      return { success: true, user: data.user, session: data.session }
    } catch (error) {
      console.error('❌ 注册异常:', error)
      return {
        success: false,
        error: error.message,
        code: error.code || 'UNKNOWN_ERROR'
      }
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      loading.value = true
      console.log('🚪 尝试登出...')

      const { error } = await supabase.auth.signOut()

      if (error) {
        console.error('❌ 登出失败:', error)
        throw error
      }

      user.value = null
      console.log('✅ 登出成功')
      return { success: true }
    } catch (error) {
      console.error('❌ 登出异常:', error)
      return { success: false, error: error.message }
    } finally {
      loading.value = false
    }
  }

  // 计算属性
  const isAuthenticated = computed(() => !!user.value)

  // 设置认证状态监听器（只设置一次）
  if (!initialized.value) {
    onAuthStateChange((event, session) => {
      console.log('🔄 认证状态变化:', event, session?.user?.email || '未登录')
      user.value = session?.user || null
      if (loading.value) loading.value = false
    })
  }

  return {
    user: computed(() => user.value),
    loading: computed(() => loading.value),
    isAuthenticated,
    initialized,
    initAuth,
    checkAuth,
    login,
    register,
    logout
  }
}
