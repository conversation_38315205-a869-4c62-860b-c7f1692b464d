import { ref, computed } from 'vue'
import { supabase } from '../supabaseClient.js'

const user = ref(null)
const loading = ref(true)

export function useAuth() {
  const isAuthenticated = computed(() => !!user.value)

  const checkAuth = async () => {
    try {
      const { data: { user: currentUser } } = await supabase.auth.getUser()
      user.value = currentUser
    } catch (error) {
      console.error('检查认证状态失败:', error)
      user.value = null
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    await supabase.auth.signOut()
    user.value = null
  }

  // 监听认证状态变化
  supabase.auth.onAuthStateChange((event, session) => {
    user.value = session?.user || null
    loading.value = false
  })

  return {
    user: computed(() => user.value),
    isAuthenticated,
    loading: computed(() => loading.value),
    checkAuth,
    logout
  }
}