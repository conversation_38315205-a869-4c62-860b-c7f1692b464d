import { createRouter, createWebHistory } from 'vue-router'
import { useAuth } from '../composables/useAuth.js'
import Test from '../views/Test.vue'
import Login from '../views/Login.vue'
import Dashboard from '../views/Dashboard.vue'

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresGuest: true }
  },
  {
    path: '/test',
    name: 'Test',
    component: Test,
    meta: { requiresAuth: true }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  const { isAuthenticated, loading, checkAuth } = useAuth()

  // 等待认证状态检查完成
  if (loading.value) {
    await checkAuth()
  }

  // 需要登录的页面
  if (to.meta.requiresAuth && !isAuthenticated.value) {
    next('/login')
    return
  }

  // 已登录用户访问登录页面，重定向到业务页面
  if (to.meta.requiresGuest && isAuthenticated.value) {
    next('/dashboard')
    return
  }

  next()
})

export default router
