<script setup>
import { computed } from 'vue'
import UserList from './business/UserList.vue'
import UserRoles from './business/UserRoles.vue'
import UserPermissions from './business/UserPermissions.vue'
import ArticleList from './business/ArticleList.vue'
import CategoryManagement from './business/CategoryManagement.vue'
import TagManagement from './business/TagManagement.vue'
import BasicSettings from './business/BasicSettings.vue'
import EmailSettings from './business/EmailSettings.vue'
import BackupRestore from './business/BackupRestore.vue'
import UserStatistics from './business/UserStatistics.vue'
import ContentStatistics from './business/ContentStatistics.vue'
import SystemLogs from './business/SystemLogs.vue'

const props = defineProps({
  activeMenu: {
    type: String,
    required: true
  }
})

// 组件映射
const componentMap = {
  'user-list': UserList,
  'user-roles': UserRoles,
  'user-permissions': UserPermissions,
  'article-list': ArticleList,
  'category-management': CategoryManagement,
  'tag-management': TagManagement,
  'basic-settings': BasicSettings,
  'email-settings': EmailSettings,
  'backup-restore': BackupRestore,
  'user-statistics': UserStatistics,
  'content-statistics': ContentStatistics,
  'system-logs': SystemLogs
}

// 页面标题映射
const titleMap = {
  'user-list': '用户列表',
  'user-roles': '角色管理',
  'user-permissions': '权限设置',
  'article-list': '文章管理',
  'category-management': '分类管理',
  'tag-management': '标签管理',
  'basic-settings': '基础设置',
  'email-settings': '邮件配置',
  'backup-restore': '备份恢复',
  'user-statistics': '用户统计',
  'content-statistics': '内容统计',
  'system-logs': '系统日志'
}

// 当前组件
const currentComponent = computed(() => {
  return componentMap[props.activeMenu] || UserList
})

// 当前标题
const currentTitle = computed(() => {
  return titleMap[props.activeMenu] || '用户列表'
})

// 面包屑导航
const breadcrumbItems = computed(() => {
  const menuMap = {
    'user-list': ['用户管理', '用户列表'],
    'user-roles': ['用户管理', '角色管理'],
    'user-permissions': ['用户管理', '权限设置'],
    'article-list': ['内容管理', '文章管理'],
    'category-management': ['内容管理', '分类管理'],
    'tag-management': ['内容管理', '标签管理'],
    'basic-settings': ['系统设置', '基础设置'],
    'email-settings': ['系统设置', '邮件配置'],
    'backup-restore': ['系统设置', '备份恢复'],
    'user-statistics': ['数据分析', '用户统计'],
    'content-statistics': ['数据分析', '内容统计'],
    'system-logs': ['数据分析', '系统日志']
  }

  return menuMap[props.activeMenu] || ['用户管理', '用户列表']
})
</script>

<template>
  <div class="content-area">
    <!-- 页面头部 -->
    <div class="content-header">
      <!-- 面包屑导航 -->
      <el-breadcrumb separator="/" class="breadcrumb">
        <el-breadcrumb-item>首页</el-breadcrumb-item>
        <el-breadcrumb-item v-for="item in breadcrumbItems" :key="item">
          {{ item }}
        </el-breadcrumb-item>
      </el-breadcrumb>

      <!-- 页面标题 -->
      <h1 class="page-title">{{ currentTitle }}</h1>
    </div>

    <!-- 内容区域 -->
    <div class="content-body">
      <el-card class="content-card" shadow="never">
        <component :is="currentComponent" />
      </el-card>
    </div>
  </div>
</template>

<style scoped>
.content-area {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.content-header {
  margin-bottom: 20px;
  padding: 0 4px;
  flex-shrink: 0;
}

.breadcrumb {
  margin-bottom: 12px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  line-height: 1.2;
}

.content-body {
  flex: 1;
  overflow: hidden;
  min-height: 0;
}

.content-card {
  height: 100%;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-sizing: border-box;
}

.content-card :deep(.el-card__body) {
  height: calc(100% - 2px);
  padding: 24px;
  overflow-y: auto;
  box-sizing: border-box;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-header {
    margin-bottom: 16px;
  }

  .page-title {
    font-size: 22px;
  }

  .content-card :deep(.el-card__body) {
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .content-header {
    margin-bottom: 15px;
  }

  .page-title {
    font-size: 20px;
  }

  .content-card :deep(.el-card__body) {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .content-header {
    margin-bottom: 12px;
  }

  .page-title {
    font-size: 18px;
  }

  .content-card :deep(.el-card__body) {
    padding: 12px;
  }
}
</style>
