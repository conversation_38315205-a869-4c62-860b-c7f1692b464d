<script setup>
import { ref, onMounted } from 'vue'

// 用户数据
const users = ref([])
const loading = ref(false)
const searchText = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 模拟用户数据
const mockUsers = [
  { id: 1, name: '张三', email: 'z<PERSON><PERSON>@example.com', role: '管理员', status: 'active', createTime: '2024-01-15' },
  { id: 2, name: '李四', email: '<EMAIL>', role: '编辑', status: 'active', createTime: '2024-01-16' },
  { id: 3, name: '王五', email: '<EMAIL>', role: '用户', status: 'inactive', createTime: '2024-01-17' },
  { id: 4, name: '赵六', email: '<EMAIL>', role: '用户', status: 'active', createTime: '2024-01-18' },
  { id: 5, name: '钱七', email: '<EMAIL>', role: '编辑', status: 'active', createTime: '2024-01-19' }
]

// 获取用户列表
const fetchUsers = () => {
  loading.value = true
  
  // 模拟API调用
  setTimeout(() => {
    users.value = mockUsers
    total.value = mockUsers.length
    loading.value = false
  }, 500)
}

// 搜索用户
const handleSearch = () => {
  console.log('搜索用户:', searchText.value)
  fetchUsers()
}

// 重置搜索
const handleReset = () => {
  searchText.value = ''
  fetchUsers()
}

// 新增用户
const handleAdd = () => {
  console.log('新增用户')
}

// 编辑用户
const handleEdit = (row) => {
  console.log('编辑用户:', row)
}

// 删除用户
const handleDelete = (row) => {
  console.log('删除用户:', row)
}

// 分页变化
const handlePageChange = (page) => {
  currentPage.value = page
  fetchUsers()
}

// 页面大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchUsers()
}

onMounted(() => {
  fetchUsers()
})
</script>

<template>
  <div class="user-list">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchText"
            placeholder="请输入用户名或邮箱"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="handleSearch" :icon="Search">
            搜索
          </el-button>
          <el-button @click="handleReset" :icon="Refresh">
            重置
          </el-button>
        </el-col>
        <el-col :span="8" class="text-right">
          <el-button type="primary" @click="handleAdd" :icon="Plus">
            新增用户
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 用户表格 -->
    <div class="table-container">
      <el-table
        :data="users"
        v-loading="loading"
        stripe
        style="width: 100%"
        height="100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="用户名" width="120" />
        <el-table-column prop="email" label="邮箱" min-width="200" />
        <el-table-column prop="role" label="角色" width="100">
          <template #default="{ row }">
            <el-tag
              :type="row.role === '管理员' ? 'danger' : row.role === '编辑' ? 'warning' : 'info'"
            >
              {{ row.role }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'info'">
              {{ row.status === 'active' ? '激活' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="120" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(row)"
              :icon="Edit"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
              :icon="Delete"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<style scoped>
.user-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.text-right {
  text-align: right;
}

.table-container {
  flex: 1;
  margin-bottom: 20px;
  overflow: hidden;
}

.pagination {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-bar .el-col {
    margin-bottom: 10px;
  }
  
  .text-right {
    text-align: left;
  }
  
  .pagination {
    overflow-x: auto;
  }
}
</style>
